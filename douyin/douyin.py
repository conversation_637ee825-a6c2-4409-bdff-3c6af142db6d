
import json


def get_nicknames(data):
    # 如果输入是字符串，先转换成字典
    if isinstance(data, str):
        data = json.loads(data)
    
    list = []
    # 遍历followings列表
    for user in data.get('followings', []):
        nickname = user.get('nickname')
        follower_count = user.get('follower_count')
        if nickname:
            list.append({
                'nickname': nickname,
                'follower_count': follower_count
            })
    
    return list



# 假设你的JSON数据存储在json_data变量中
# nicknames = get_nicknames(json_data)
# for nickname in nicknames:
#     print(nickname)

with open('douyin.json', 'r', encoding='utf-8') as f:
    json_data = f.read()
    nicknames = get_nicknames(json_data)
    for nickname in nicknames:
        print(nickname)