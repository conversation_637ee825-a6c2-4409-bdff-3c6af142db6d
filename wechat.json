[{"title": "Android常见获取设备标识方法现状", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt4syCqtt4qGiaklVP0XyBEib7sCUibTzNT5oFt7XmeQxSaNiaBfBm7NHm1ek2oHmM6CPBfJciaQ4iaeEWjA/0?wx_fmt=jpeg", "source": "https://mp.weixin.qq.com/s/k19uLZEGaFGdiHN-wwmXFA", "tag": "android", "remark": ""}, {"title": "开源网站建设工具，做网站从来没这么简单！", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/ciaOAxzQ9MXK9sSjZjq9MEhB1PjDcXmVoHiaOJAaC1eJMoQicpgl1Hic3cos31Pf0MZlfUAxfLZjwbx8tKtOBicWvqg/0?wx_fmt=jpeg", "source": "https://mp.weixin.qq.com/s/hwus2hAZNvpR51zDF7Ejyg", "tag": "android", "remark": ""}, {"title": "一个很火的开源数字人项目", "source": "https://mp.weixin.qq.com/s/tREGsJ2EB1Ewk-jz4x23YQ", "tag": "android", "remark": ""}, {"title": "a16z 低调投资了一家 APP 开发公司，所见即所得", "source": "https://mp.weixin.qq.com/s/d2-x5tTjHNtDcCWkaKzUOw", "tag": "android", "remark": ""}, {"title": "Now in Android ！AndroidApp开发的最佳实践，让我看看是怎么个事？", "source": "https://mp.weixin.qq.com/s/ZpQsA0QWXvRTlJbLMQ1Xrg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt5lytzuaffKia7kHrvr9f8RcqrTngPibpW9qVOEibVUE1TbHmLzjpNyVib99EficDn9XibhxPWlicicebGAFA/0?wx_fmt=jpeg"}, {"title": "Android应用发布前开发者应该做的安全检查清单", "source": "https://mp.weixin.qq.com/s/yMDUS00C3qUkCkFRTEawOg", "tag": "android", "remark": ""}, {"title": "效率爆表：30款 IDEA 插件，让你的代码飞起来！", "source": "https://mp.weixin.qq.com/s/yppilWCkh6aolomsOvO28g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/eQPyBffYbudqgcjWjzOvqjGT1S4IDlUs9iaic4ibAGTLeKWY8sGnAQrkNIk7gaOMXtYiatwaDkcJzLoP5oDzATMxjg/0?wx_fmt=jpeg"}, {"title": "Android Studio 超实用插件推荐", "source": "https://mp.weixin.qq.com/s/6-tBISsJjyf8g-TcNcbRPA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKoYjMNZzfEiaszu6GN9sMRrl4bx3YK7zWnLYhtXfzspI9tZTwUiaHicJ3zKI6Il1eOyXOeADhEiaLrktA/0?wx_fmt=jpeg"}, {"title": "从Java8到Java17，这些新特性让你的代码起飞！", "source": "https://mp.weixin.qq.com/s/7myhkTs6IwZebr9oxss6_g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/eQPyBffYbueJgujcckrqGiaC1Uaia6cTzbE15cVod2hhpVwNozKdC1xdqEnqex5u0O9IvCCFmcRuSunuicia8GNkGQ/0?wx_fmt=jpeg"}, {"title": "Android KTX 实用类库介绍", "source": "https://mp.weixin.qq.com/s/exO06r1ylvINp37C3ArlkA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKoYjMNZzfEiaszu6GN9sMRrlE3gRAKfUmA5ekyL1S5rMeAQWVmGVu2bAgRliaia05gqlmyeQJQMXxl9A/0?wx_fmt=jpeg"}, {"title": "用Native方法加密APK包所有字符串，安全性拉满", "source": "https://mp.weixin.qq.com/s/QRt2RP4UzSc-CCNhoPD8Hg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/cmIeo6x93kMvV2G9ShNWtmhOrVgndTgHPongBDkwBmfrpTqv3t5iac1nyqSJVN0dlwCwDQ1piaPlQS2zEFJR1HDA/0?wx_fmt=jpeg"}, {"title": "使用Jetpack Compose时常犯的一些错误", "source": "https://mp.weixin.qq.com/s/FtDgsCjue7srzTo_754CTQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/7G6wAxO5rWCPlum0M8EBmQl1umOKSiccFUOT3ZY8qtB7P2Okdn7nFtmJgR6lJmJGaNzCqibALuTK8AyzZzpBOlqA/0?wx_fmt=jpeg"}, {"title": "强烈推荐 Android Studio 2 个易被忽视的效率技巧～", "source": "https://mp.weixin.qq.com/s/kIX1aKKkoFfC4UmprTBehQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/GIUVCiacpxgO5F21blsIPffj6ufkkicg1q4Q4Shq8MfdF5LF1ctZWIbF1SWsC4wXdP6VQQCiaiaYzsA28nw3H0BjHA/0?wx_fmt=jpeg"}, {"title": "通过开源项目为你的华为手机上重新安装 Google 服务", "source": "https://mp.weixin.qq.com/s/amPB4-GNouw3PAS91IuL6g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/trqxtrS1h0sA5kFkockuK7DvdKMfTVmXib56sib17yzvicZhfnKHyRsuIguOEGlzZicygzxCDKoyRfvibkRzdJbLNjw/0?wx_fmt=jpeg"}, {"title": "项目推荐 | Android 架构组件全示例", "source": "https://mp.weixin.qq.com/s/YZGxLZeFDzDSKrcRge_eTw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3IH4baicMQhB7RdibRKd8gwRsuX5GmeiaVs5ASdw64TEnuEJ5wJ55yfc8BQ2IicMo2aNMO8YctvFN0UmA/0?wx_fmt=jpeg"}, {"title": "迈向 Android 架构师：模块化设计原则", "source": "https://mp.weixin.qq.com/s/1Dp7hLwijk6ZKu5ErssN4w", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKrNt6gRt6yzicDb2FByGaDI17un5pcF7ibqfbhrl8EdSKMj2hg35Fc6rdonViaiaYGz2dP08FueXnj0Xg/0?wx_fmt=jpeg"}, {"title": "手把手教你优雅地实现 Dialog 弹窗", "source": "https://mp.weixin.qq.com/s/zmOt6UoaezuksBEeTl43VA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKr8icyguRsoPTR2RlqEbu629kI27BCr6HqfNvxlkOAiaVHLa4GycBdZdV6C9D4FDBajQkiaNkZ6jq6Xg/0?wx_fmt=jpeg"}, {"title": "浏览器点击链接打开指定APP详解", "source": "https://mp.weixin.qq.com/s/iYQMAu517s3X3M53dZryWA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/cmIeo6x93kPwuic6ukylXayWSVibk8UxkgkReGcrOfzKccVUz80MibHgF5zPOXiaC0oia9Q04RSp7gSj8x9hgQjO9MQ/0?wx_fmt=jpeg"}, {"title": "Android OkHttp使用过程中的一些经验总结", "source": "https://mp.weixin.qq.com/s/3LPmLUT_UxQML1P7zG5giw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt6vch9sGDvqL4Lhq1zNdD5JAGWibnFoVO0bwxq4oVC3hKq15bKsibL0rI6be17QecL7GmxG0JhNIUbA/0?wx_fmt=jpeg"}, {"title": "快来看看如何让你的app可以分享文字、图片，甚至安装包！", "source": "https://mp.weixin.qq.com/s/iMjJxkAKWVnfgCOC6oyjyA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt4VUeFuHCupkRZM16kqdzVuf56QsvPMzTZlRY9ORiavABv82y5EbgliauEaic1kOicuM5KT6c052Quv5Q/0?wx_fmt=jpeg"}, {"title": "基于大模型搭建的微信聊天机器人", "source": "https://mp.weixin.qq.com/s/Ban2oUqNEQgYuoFwLKIdqA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/ciaOAxzQ9MXIAhGtWCicHZlK7TibOOibsHcqUfJq1DZdibiaZhO6cQly9dTFRzsfFt9ZKjw3OzkLAw8D4kwrkwImCZicA/0?wx_fmt=jpeg"}, {"title": "CodeLocator : 字节开源的 Android 开发利器", "source": "https://mp.weixin.qq.com/s/QdWQQe_uV2LAVV2B81IknQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKrsL4mFxV28Hr7y96unFjcDENIk74jvqzHkzwIIfaEz1CDBjFBmKyQao1kDcDoh3TQrGAJgOSN8ibg/0?wx_fmt=jpeg"}, {"title": "WordPress安全策略，懒人必看", "source": "https://mp.weixin.qq.com/s/gnaM7Raloai2dKyJPVBJUw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/ujNpgsuqhXbTA6dGSD5YGxn1MdCTgTV0lNVNVniaR5PL9K8G6KibO9uzZnkBsOBWf59iahn0Y9SJt5Cbupm0rXbNA/0?wx_fmt=jpeg"}, {"title": "Clean 架构下的现代 Android 架构指南 | 开发者说·DTalk", "source": "https://mp.weixin.qq.com/s/wDGUPkHQKrkKO3ZCX8aaMg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/icFp8MFO4IKwlPLwP3YvcNJfia4N0j9jV0RdickNdLpQdPrwa8tPDDh0KreGib6Ps1pkmohWx4rl3QLqoP0UnKCYWQ/0?wx_fmt=jpeg"}, {"title": "Android开发如何在多模块项目中统一管理依赖", "source": "https://mp.weixin.qq.com/s/jHfJI79qQOKrJtk9_kjj4g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt7iaVKMXEglsq9akFVzfy1dZfdu4xxuiboibQXiayOkqjFAK6u1Vy7qsnGcENKylrh8Vnop5EDpN4YH5w/0?wx_fmt=jpeg"}, {"title": "Android14 适配之——targetSdkVersion 升级到 34 需要注意些什么？", "source": "https://mp.weixin.qq.com/s/GWtY8qV-BhTkPEnSbIjTHQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/lMeMzb2PJnwXTmc1uJ6c31TmgI9tXHNd4DEAvIBibJ0Uo8Vq9mte5Xp7S4hZVR8zpaqw8VNompBujdNbjnCLksQ/0?wx_fmt=jpeg"}, {"title": "效率提升：如何让 AI 帮我写 Android 代码？", "source": "https://mp.weixin.qq.com/s/PYnasgzmb5rCNO4R80VeTA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/vEMApYVjEzdzvQC7cJQXv5qicJcE9JicoxwTegw9P9LlgY44SnMEQM3ZdDeU6VETXNwjuM9euS6OxwyIaqBLgIWg/0?wx_fmt=jpeg"}, {"title": "腾讯出了一款 数字人核心部件- 开源免费！可以搞数字人项目赚钱了", "source": "https://mp.weixin.qq.com/s/AouhvhHb5KagK6zWHFFHLw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/ciaOAxzQ9MXIfPFuke8kFztNichnCiaSL3hiaqD5TKPld9US5fQic8lpe37UzNXUtFcKfxY6rP4L9MTCxC17zk7PkyQ/0?wx_fmt=jpeg"}, {"title": "腾讯出了一款 数字人核心部件- 开源免费！可以搞数字人项目赚钱了", "source": "https://mp.weixin.qq.com/s/AouhvhHb5KagK6zWHFFHLw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/ciaOAxzQ9MXIfPFuke8kFztNichnCiaSL3hiaqD5TKPld9US5fQic8lpe37UzNXUtFcKfxY6rP4L9MTCxC17zk7PkyQ/0?wx_fmt=jpeg"}, {"title": "一起看 I/O | Jetpack 新功能一览", "source": "https://mp.weixin.qq.com/s/9NW75ZtiMDsFA1KrHL5ohw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/98ia1TMnq0gqXqKHF4LTj9zVHMa2ghHEW9bTZX20KqL4IKcq0SU1zYgryhVF0Le8PF4NRdWCsguiaDsxvZ9XyyJg/0?wx_fmt=jpeg"}, {"title": "Android 文本识别：MLKIT + PreviewView", "source": "https://mp.weixin.qq.com/s/196-FxDvlfaTu8gZfAW8ig", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/e74tMHQhRWXKdAxAFR1GD0tEib8eVBRpLzqDLQvE2gIENicgpxAdic0W1ncVHG0iadkMyOYHSG3IjftJZ3PJTFfCBg/0?wx_fmt=jpeg"}, {"title": "Android现代开发推荐 | Android Showcase 2.0", "source": "https://mp.weixin.qq.com/s/ONgiGO3DzzkSsgb1fCw1ag", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3J29j4yWBlKI5LtceoL3Uic5bUIbiaYOJUzppBVCzkbNHT7veqlCuOBMibicUiciaGlUvOYH9VqeTR0qjEw/0?wx_fmt=jpeg"}, {"title": "Android项目推荐 | 使用Jetpack Compose和MVI构建Book应用", "source": "https://mp.weixin.qq.com/s/3htwVDUiXauoAlxd_CGrRg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3KtgSJym1Pzw9Q8C8xwBVWma5F8Jwy864jicNkTxYCVgLsMGicrxazkrlQiakaSkYvB0JK43gmBbgsow/0?wx_fmt=jpeg"}, {"title": "Android 逆向之脱壳实战篇", "source": "https://mp.weixin.qq.com/s/7aidVO9kRrtd095K1T5ZeA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/cmIeo6x93kMt1yKPeNGvicdZtkqLZtTQictdQ3nYxD8JLAwhlP5kGn7m5kS4fDB6kQo25ibdouKLIkKjQfDfdFCKQ/0?wx_fmt=jpeg"}, {"title": "Android 最新官方架构推荐引入 UseCase，这是个啥？该怎么写？", "source": "https://mp.weixin.qq.com/s/oiMUAoXoiGXjDFkRo6o9SQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/GIUVCiacpxgMxnv67d9bKpicezICAc8ibqzjl2wdxSks6cqOJYbVaf3Xic84mVBumC4mibLeoOCb0F6Jkwicv7Uw2ib7w/0?wx_fmt=jpeg"}, {"title": "Android APP合规检查工具", "source": "https://mp.weixin.qq.com/s/fSP_19xtya-K1JYLCqYelw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/LtmuVIq6tF0fGvlDBPsztxsIDdH4bAeUPx1ibgUsBCxOLDiboicWjg3tYNyfpWSI5ndomohho0ichupoRrlAED8prQ/0?wx_fmt=jpeg"}, {"title": "Android APP合规检查，你可能需要这个工具~", "source": "https://mp.weixin.qq.com/s/7DUAiTKzkFGbi3wzDywlfQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/yyLvy204xWibupscq6iac5ttgibIToa2hvfPpkQmGahD5Ag6MPCFniaBdic5QdCNY2ziaiaZ3DDqBibNsicwkJrMCuXbibqA/0?wx_fmt=jpeg"}, {"title": "Android的MVI架构最佳实践：Model和Intent封装", "source": "https://mp.weixin.qq.com/s/YNSIAXyFKGU1H1KR13PbiQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt4ialmo7tNrSsc7TOBAm7KJicsiaJAInNYWMamJezp5WI9egTE7wyoj2bVjcYib8k2V6EibnD2rzEwvBBg/0?wx_fmt=jpeg"}, {"title": "Android 最新官方架构推荐引入 UseCase，这是个啥？该怎么写？", "source": "https://mp.weixin.qq.com/s/x3oKpkEp2lwlVZSgPewF_w", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKrLrd9j1SmeIxxjdK4YibicCCqgrrrJ7s4pexr1ovfUQxQ6WP2VVawq7HYjB0gOaWDzD9qEpTgyiaWKA/0?wx_fmt=jpeg"}, {"title": "如何在2024年编写Android应用程序", "source": "https://mp.weixin.qq.com/s/ZaNdQvEnfCFfy4rIoKzh0Q", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3KnQkHJ3zfwcsdJYlBeLmSAE158540Cxon7meiacOK7hIUQ75kTiba0YxBgPkZ1CiavDkt0LdGkgml5g/0?wx_fmt=jpeg"}, {"title": "Turo 使用 Android 开发者工具和最佳实践将应用启动时间缩短了 77%", "source": "https://mp.weixin.qq.com/s/oakKIp5iCHrMgFCOjx5fzA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/98ia1TMnq0gr7huBrMUicgOFLMGgUdYFmteWUBzBEnY9b72SFxAlicsw1rGNapu10OeDokReGloD4t1bXWhniaqsug/0?wx_fmt=jpeg"}, {"title": "黑客心理学：社交工程中的四种情绪反应", "source": "https://mp.weixin.qq.com/s/7RKiZ7wJP4BCiCinbICzcA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz/7WSicCia9GDbCNLFZWEBekrhUdcCNzybTvAQZJQ5c7ia6ljhfkOsEezPibCBUATv3WLcY3tbfeRsANBckwGULt5IQg/0?wx_fmt=jpeg"}, {"title": "我又写了个工具，方便使用WhatsApp", "source": "https://mp.weixin.qq.com/s/pzt3vwje5_X3dmPvXdBMPQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/ujNpgsuqhXa3LDgBdpQPqtuQibIbabuSleqicw8FCtM8OzOB1rskiaYUbOme59x8s876OFlhJeafxY27v3n2VrNTg/0?wx_fmt=jpeg"}, {"title": "7个杀手级IntelliJ IDEA插件", "source": "https://mp.weixin.qq.com/s/KkxfKXZ7zZvrMv0y2Ua1bg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/S8yWrB610ThZl6WTqicg5z0vN4oMvnicWpPibiaTniaAPtg18fnQa0Cn9vcClx2UYKnicc9BAh1jP9Aem7abEmJhhSdA/0?wx_fmt=jpeg"}, {"title": "现代并发编程的三大巨头,你用过几个?", "source": "https://mp.weixin.qq.com/s/_QO0uf4iH-XL5b8CidDCuA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/ia3ACbg7aawyEjGNZzDVfRXTe6J6zTO4Y0ZqbX49hH5ZXtgBxn7gQavX6esYausNeqv7phmR7viaNIjbGxI8uIpw/0?wx_fmt=jpeg"}, {"title": "渗透测试 | 信息收集常用方法总结", "source": "https://mp.weixin.qq.com/s/FCp7N_JhNP4hNff6ch4o5g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/BwqHlJ29vcojiarL530RTyIqSuVKJyh8e8FZicbicQIia6akO9wWuZ01RZD5aYGjpLsMysRWN3AoJcSTKHeWfACF2Q/0?wx_fmt=jpeg"}, {"title": "10分钟带你用RecyclerView+PagerSnapHelper实现一个等级指示器", "source": "https://mp.weixin.qq.com/s/yXVgInWbCFfddeAKh2JOMg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/e74tMHQhRWXbz7cJd5ytGFxjvEwgjKwFwoAzPS05AyqOGCmImxsjHpDcohuzRckkib4g2Rj23rwQmKFGZcJMDBQ/0?wx_fmt=jpeg"}, {"title": "Android | DownloadManager下载任务管理器", "source": "https://mp.weixin.qq.com/s/VZC0gHwECO8zCH1Ug4Mczw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/pMoVPfrZSnBUAvsrOkw7P1nq9Te4XzAfXJb3lyX3R4RAwDn69icXwZNomyvyORvDmXQichHIrRExiac1JJq0ZewPw/0?wx_fmt=jpeg"}, {"title": "国内个人开发者太难了，APP备案保姆级过程", "source": "https://mp.weixin.qq.com/s/7ujETtMc3yFLgNXSJqb7hQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/ZFbd8icuQe1LFQymxSw1pQJ9NWBvSuCeI2htz6LicmhlcHjCW8ZYcfrdzFnnibKtFLbuU1ZEdwaCIUE2r9kWapYZA/0?wx_fmt=jpeg"}, {"title": "Android如何保持后台运行——唤醒锁机制", "source": "https://mp.weixin.qq.com/s/1gf_MtKIDd7FMlEPMSnh4Q", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/bGUyHwT5U6tFQ0ibb2wXTnp7T8d5TyrrzUCgu3I9jdd64FJFRT9oez81Vxl7c7EjP7xT8c5eDTWCooG2UFfGrPA/0?wx_fmt=jpeg"}, {"title": "Android应用进行插件化，有哪些关键技术？", "source": "https://mp.weixin.qq.com/s/7mm9_g4XphY3FbE79L8yJQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/pMoVPfrZSnBZta87W9NDVowGT3PyvZY60xl4pf5esibW4yXpXibTADE9k41jeD4AG2EazIkTtbyBtgsrwxnE22fA/0?wx_fmt=jpeg"}, {"title": "Android适配：判断机型和系统", "source": "https://mp.weixin.qq.com/s/xH-TiJNi5F9OXAYXq1Y-eQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/yyLvy204xWibIlq5FiapXqicUC67CeyEhgI40aZTx7waePawQVSsyJxFa2TK2Y0iatNqqCmH7bubO4nVP9kPS3seCg/0?wx_fmt=jpeg"}, {"title": "初步探索安卓MVI架构", "source": "https://mp.weixin.qq.com/s/4lo73NjLK8LAqxVnIkPevA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/rxLIic6e5g8Rich3n8qJdiaz4XB3eoBnUmBn1cOpQZSQ6ics0a8iaULeS8GrPfYwCaBjCw6w2F11QbicF4HkWRBa9sDA/0?wx_fmt=jpeg"}, {"title": "如何使用ConstraintLayout代替三大控件", "source": "https://mp.weixin.qq.com/s/wwFDEwxv0EzcwUmG3XMfDA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/e74tMHQhRWU13WMicTibjpzBvWKTNZzYbSVdtcbY7x8CdfDb3tnNqTfWsmnn9Zgw1l4mG0vD6mht0H0WRSZxNcMg/0?wx_fmt=jpeg"}, {"title": "Clean 架构下的现代 Android 架构指南", "source": "https://mp.weixin.qq.com/s/UpVLIbjnBDuyy6cuazkPKg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/T7nXvV9Ysy5cJjQvcqo7MGEQa0XM0lO7lJZuibYiaMFJkEibm8znKcd2qEDOcMe4ZEicxGonQvAbFwyy42noNYp8aQ/0?wx_fmt=jpeg"}, {"title": "2024 年后端开发人员路线图", "source": "https://mp.weixin.qq.com/s/XgYdNuerbHjEbGE0p0nr6g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/9RM1R8UKDIYrnqtEt4ZInJzeK0hrN8djXicUnSiajT4aQqiceUxDPgmWQYUTiafybKavzTtDMQ6sicdB8VDjKp9Q7XA/0?wx_fmt=jpeg"}, {"title": "Midjourney劲敌来了！ 谷歌StyleDrop王牌「定制大师」引爆AI艺术圈", "source": "https://mp.weixin.qq.com/s/WEURYCA3hdloBojbj-eCxQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/UicQ7HgWiaUb1JFktcxnDVCBe43k0KQ64UU8iaZec5wczPZKicQ8vTiaJPFaX0NUVelGic5M8V9fXPPkyANW9ous3t8Q/0?wx_fmt=jpeg"}, {"title": "腾讯零反射全动态Android插件框架，一文解析", "source": "https://mp.weixin.qq.com/s/0D5RRrtJiWYfPJ0BaS1fPg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/cmIeo6x93kOaE2tXE8YVulLNyugeCNM7WlOhXp5eLu9FNRodj2e1XR2NhstyqYia8UrqhdPH8rM8ppxGwKm1ialg/0?wx_fmt=jpeg"}, {"title": "Android Studio 实用插件推荐", "source": "https://mp.weixin.qq.com/s/-odSeh1I78H1kDsm5MHkag", "tag": "android", "remark": ""}, {"title": "LLM as Co-pilot：AutoDev 1.0 发布，开源全流程 AI 辅助编程", "source": "https://mp.weixin.qq.com/s/2qwx6l32_-P4TfLefMFbfg", "tag": "android", "remark": ""}, {"title": "拥抱创新：用Kotlin开发高效Android应用", "source": "https://mp.weixin.qq.com/s/no4mmKFwBd0CgoWo3RLB3w", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3KjPbyhBKAQXrCcum0yicasJc8hNleEDiadhABAuhHYXlcPO73hbryCib5CResTxLHLPEic5L0yQHEEzw/0?wx_fmt=jpeg"}, {"title": "Android 性能优化，网络预连接", "source": "https://mp.weixin.qq.com/s/ZFXb46bJcIL0q2gOWYl-Jw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/MOu2ZNAwZwNIWZ2Xm9Rt5aa0hZnyEaJZtYj92qKLtf9HlkCbyt79K5mmNwDZKM9K1noIQZRYX0KAqzU7F5Pkrg/0?wx_fmt=jpeg"}, {"title": "APP抓不到包？", "source": "https://mp.weixin.qq.com/s/s42X7POM7bAO5YszJTGBKQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/LtmuVIq6tF29xpovDf3C8wicGPibgH8H2ndBdxKiavPywp9IeV48htNiaSk9HOYokPD2aJ273FoBic589AxeNDwCF5g/0?wx_fmt=jpeg"}, {"title": "面试季：Android 常见内存泄露问题盘点", "source": "https://mp.weixin.qq.com/s/kdzYQI0tmy9aupS6Cxdfsg", "tag": "android", "remark": ""}, {"title": "无障碍就是牛，简单封装AccessibilityService写个库", "source": "https://mp.weixin.qq.com/s/qHnlmCO5c6JulZWVf-4Kyg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt5qHrYH6RCQFKlQWLE0icBBrIbjSRvvQtJ5nLQu8sKMSlKh9m8ZspM6b4ZiboPRw6z0wmdNB0ic4Bdow/0?wx_fmt=jpeg"}, {"title": "推荐几个开源项目，也许对你有所帮助", "source": "https://mp.weixin.qq.com/s/1UfqiJgG7DvWR4u5TMShAA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/pcpDnUibxSljhNkibyMgnzvPNkDeZ2ybDCBGRdme1N9YicgViaIibDmCgCjXbibJLpxmdqpWaa0k3OJxQ7YUO5xuUr8w/0?wx_fmt=jpeg"}, {"title": "Android中构建多视图 RecyclerView的正确打开方式", "source": "https://mp.weixin.qq.com/s/W1oKKjN4iyaZvIitRksRRg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3IP79k4EVpywZDy7XfNPwYDyLvoPY7n9XNq3811qmkyR0A2bHia3d4RkEibS8rfpKDY5lNx62a0WMZw/0?wx_fmt=jpeg"}, {"title": "程序员如何躺着赚钱", "source": "https://mp.weixin.qq.com/s/7-_VGWP_SDxDiqLCyYS3lw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/V2ozYz3DNvS8QL4BQIxgIYROIHoIxcYShLHEbToia0ULPzjMN5DCz91yXMOx96kkdw1qKsIvhDzxckSn8P5QibEA/0?wx_fmt=jpeg"}, {"title": "重磅发布！Google官方Android现代开发指南来了！", "source": "https://mp.weixin.qq.com/s/hx2VF4KuMbpDg2FdPjfvtg", "tag": "android", "remark": ""}, {"title": "爆表！RecyclerView性能提升200%，异步预加载大杀器！", "source": "https://mp.weixin.qq.com/s/TZADNT9nq6COgvhN6bj5Ew", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/f7m82SbJnWERupCLOXrHxpVILn9yU1g4VSibppFia42AX9F38nSAW5IuJQMRuEkBZ23ibnwAV5ZLicW7Dx5nG9sbtQ/0?wx_fmt=jpeg"}, {"title": "在Android中实现动态应用图标", "source": "https://mp.weixin.qq.com/s/4JaIENZ86Ws1UKKpivl_aw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3IAwC9N6gBHBwWibRG28gK83thHvwttNFrhp692TON21Sg9CFZ7ReIJrtcE8BXsCreLtDOTP9ElC7g/0?wx_fmt=jpeg"}, {"title": "升级targetSDK为33后的十来个坑 (工具篇)", "source": "https://mp.weixin.qq.com/s/YJsNYHeh3A1gNbKueJuy1w", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/yyLvy204xW8FR8ibTwbWsb35zPwuIvOeNfz7icDY5NtnfBKzJRKcmMkIRVkIF81iavXLicPJRfwUOd1Fe1Qy8LBdcg/0?wx_fmt=jpeg"}, {"title": "Android开发现状？你需要知道的最新趋势和工具", "source": "https://mp.weixin.qq.com/s/vYXoLgjY3dvBSUVfJvReCA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/xP7JY2OM4TSPOCYfGibPIbNTygy12iabUkCSjg2tZPAKeDzK84gichNKP5CTSpQD2PZTRFdFtNlnMwlx4PqtySn2g/0?wx_fmt=jpeg"}, {"title": "APEX：开启Android系统新篇章的应用扁平化技术", "source": "https://mp.weixin.qq.com/s/Ce1kngATbth3aIXz3KK0Bw", "tag": "android", "remark": ""}, {"title": "修改了Android Studio 中的这两个面板配置后，代码写的更舒服了~", "source": "https://mp.weixin.qq.com/s/gohyp2JMOPotL7UrstYKKQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/MOu2ZNAwZwOk9a9wqDQS0WUKW7iaQXU8nT7SMkPHFgFoUibfxF6aBQ9tFxlt89bLBKmqibbuMYhHDVxSibG9gbvw3A/0?wx_fmt=jpeg"}, {"title": "Android插件化-启动插件的Activity", "source": "https://mp.weixin.qq.com/s/iS7ZGbcm0t5QEAXvnUOIRw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/cmIeo6x93kNSECsWzy6kyKLOJ9mAQVxRNriau6RABZmFvIKqmYQp5c09oBt0fkrX9JtMPLoS63WhN8sldPA9CCQ/0?wx_fmt=jpeg"}, {"title": "Android子线程可以更新UI", "source": "https://mp.weixin.qq.com/s/Gsk31ZNhnC7BdmYXqsIXbQ", "tag": "android", "remark": ""}, {"title": "为什么移动项目架构设计很重要？", "source": "https://mp.weixin.qq.com/s/BAfUgWZgJruQvjrpkUC93Q", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/7G6wAxO5rWCSpg3m4hbDamtiaDqlCUjnHfb9mGM93wUnKicmymQhFaEyPWtaXCDfHbTBIt8OI0vEoQPJibtiaUmoew/0?wx_fmt=jpeg"}, {"title": "3 个令人惊艳的 GitHub 开源项目，诞生了！", "source": "https://mp.weixin.qq.com/s/OKIwW0E1XZveDPfkof0nPw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/uDRkMWLia28gBzKzoaich8TKQv8eHXpHxmLiaun7fRD3kceuOSMJicKR5lOc2P9NMtNYtwrvJpoUZKQgvziaJtNTicicw/0?wx_fmt=jpeg"}, {"title": "2023 版现代 Android 开发概览", "source": "https://mp.weixin.qq.com/s/AjXZ62YfQn-8RGmw6wlKqA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKoqiaKafNTqDB8GgJg5GBsV6QsOpDFL0Y2lxsibQBMIn6WickibonPylO19TzGibGiaqWY2GkM7kdWPXEfQ/0?wx_fmt=jpeg"}, {"title": "2024年App从备案到上架（国内各大应用市场）全过程", "source": "https://mp.weixin.qq.com/s/7tIW__dEjZ5XefG3rNrqsA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/yyLvy204xW8I5hiaPVhfhIQAgwON5aGmAWo4sSDUia3agPAM17AwjxBbhJFM3uIvq7b4sSXqvcibb7QUgwc7KhO3A/0?wx_fmt=jpeg"}, {"title": "实时的软件生成 —— Prompt 编程打通低代码的最后一公里？", "source": "https://mp.weixin.qq.com/s/pmiHD_FxrgrNufQRluFNjA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/aG1vFUMgRBC9FY9H7nyv8QrZicasJgNzlqGvcribdWfiaIicQkE5ibkyJtf4vvwZptWzU5yicaLdMnOAdYMmVFRcPzIg/0?wx_fmt=jpeg"}, {"title": "为何大厂 APP 如微信、支付宝、淘宝、手Q等只适配了 armeabi-v7a/armeabi？", "source": "https://mp.weixin.qq.com/s/QL4aYNPb63erK-sqP1Dorw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/QFjUqsncFKlRDRs7ibC5TyHGPLqyt4yLZ95ibAU12NE6H6bZKMH3Etz0tU5obmP84u3hhEGkZttIuXcFUIq30tgg/0?wx_fmt=jpeg"}, {"title": "自己动手训练个 LoRA：代码辅助助手", "source": "https://mp.weixin.qq.com/s/OxaOIRmNqQoRhbsXYDgwLA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/aG1vFUMgRBDxN6Ejb5cwWtgME8ta0HbB5GxqJHXIXLWOuWVtj7nVKnFtGetdCRPkNyjPyGK5XccicamwFadmsoA/0?wx_fmt=jpeg"}, {"title": "Android 如何获取有效的DeviceId", "source": "https://mp.weixin.qq.com/s/ird9PqCZ_NNtQTPH7CQSsg", "tag": "android", "remark": ""}, {"title": "使用Android架构模板", "source": "https://mp.weixin.qq.com/s/umOKNNDJfOzQ5DqN9WnwnA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/hfx5bHOBa3JxfbNLuvT1VaYIukgsDOuEuO6f5TicnqK57woxoeXIevq0cJribWymPKZeBRibGzBV4Knvaeabaw0iaw/0?wx_fmt=jpeg"}, {"title": "Studio Bot - 让 AI 帮我写 Android 代码", "source": "https://mp.weixin.qq.com/s/nSIm7vC1r4rapo06V6QJ5g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKoE74yYcOdpI07Hqticf2a8SgtzxWSOeabrInLRygl6t5hh48gSJOwjSmeaQibW84Fh6bNYsTPTOBBQ/0?wx_fmt=jpeg"}, {"title": "大型Android项目架构：基于组件化+模块化+协程+Retrofit+Jetpack+MVVM实现WanAndroid客户端", "source": "https://mp.weixin.qq.com/s/3Jh0BgvsnOY-JvzE1T9Q4A", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/dYf4h7SUYvPqjQmm0vTgOYUrAXP9PsY1vWRH2OIbBKp2ynyyTnVTeodicOLLGNr5n8mTkCicUOibuDkYSKIibpjEdg/0?wx_fmt=jpeg"}, {"title": "40 行代码拿下拉勾网招聘数据", "source": "https://mp.weixin.qq.com/s/AV-NxqZJHoDJKo3JY1B7Xg", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/dkwuWwLoRK9MENPtevib8YXBIHS5eUN3oB6X2XQxvK3TZsO4kDSF3eaiciagTMQHkPXHbIyUvrzUC7mCePYkLPiapg/0?wx_fmt=jpeg"}, {"title": "[安全测试]如何发现攻击面并进行测试", "source": "https://mp.weixin.qq.com/s/JPe6rCAEisNSaK2m4KO5UA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/FhcEoH8bpbKAagMbECbnRdorJG9rAhGqeibpdGBSUC8HV4xLaqRGk6D4RGgib45kEzic9fC8VWwicyQWiaINO4U8A6w/0?wx_fmt=jpeg"}, {"title": "【安全圈】北大团队搞出ChatExcel，说人话自动处理表格，免费且不限次使用", "source": "https://mp.weixin.qq.com/s/_IdhKe8kZnjVuo08Mt2BPA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/aBHpjnrGylhTFk2gSibqUBtDChjBZIamWSLvPjP1jpMAREFhXUSKCMo7XGvBvKGRKKyGCtDOpYszo4uxIuy3oPw/0?wx_fmt=jpeg"}, {"title": "当我用Python写了个抖音自动点赞脚本，直接接单赚到手软！", "source": "https://mp.weixin.qq.com/s/JRI37ouWq63fhb1ckN7MxQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/pOTh2wdMWXqIekOuELYOib5lmBsyGdlnDHXhv4H3JicUzUJlHbtOneJe5q62Fqx2oiatra4yIicXWudddIibZY9xASg/0?wx_fmt=jpeg"}, {"title": "ClickPrompt：一站式 Prompt 学习、设计与运行工具（开源），支持 ChatGPT 等", "source": "https://mp.weixin.qq.com/s/EA40U5CxtKoyshgH_R1bWQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/aG1vFUMgRBDq1hic44dBk1rH27XexkybTWDLGiac54uD13qYQFWuptPks8MeUFiaHESDkwCOibJ6QQpicHcVzOyzkVg/0?wx_fmt=jpeg"}, {"title": "Android 架构实战：MVI + kotlin + Flow", "source": "https://mp.weixin.qq.com/s/TWIlYfvP83-TigEPFJCO7w", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKo8vfQc7p6ony2KicwduG8ekibHgXk3AxONHOIoib4BVhRa6lbCGOg9iczm3gUnrViaSVLUm7O3iarSYsAw/0?wx_fmt=jpeg"}, {"title": "有了它不写Controller、Service、Dao、Mapper、XML、VO，全自动生成！", "source": "https://mp.weixin.qq.com/s/LZoxRd0TrCHHAvPh-VRWiA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/icrN5ysXiaicVfrYPcYMTnwpiaTrC09PAUsGbh4toFj3T4oGRAodOnkDiaQGrBjcuYibk3G5dgjpZzibUV5LjY9XQ9kiag/0?wx_fmt=jpeg"}, {"title": "5 个令人惊艳的 ChatGPT 项目，开源了！", "source": "https://mp.weixin.qq.com/s/4UJAY06rQETuX4zEMrJglQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/uDRkMWLia28gBrdhricOpL2SibMS4G7ibNX1z3lqhibVw5U3VocGOZcMLkPgRMdqbtyNcJqccJEuExiaAnichjm404UYg/0?wx_fmt=jpeg"}, {"title": "有点恐怖，这个工具能让你在 20 秒内，克隆你的声音，并转成英语！", "source": "https://mp.weixin.qq.com/s/-7uB4rWvuJH4cI8xZnE-mw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/uDRkMWLia28jbw6ySFwFHBSUpfsgQ6O8JoRQvZbyCVcs7ypS0hwkOl7QmlFrYia2VIQU819KL5q4nGY4D2KYPY3Q/0?wx_fmt=jpeg"}, {"title": "为何大厂 APP 如微信、支付宝、淘宝、手Q等只适配了 armeabi-v7a/armeabi？", "source": "https://mp.weixin.qq.com/s/QL4aYNPb63erK-sqP1Dorw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/QFjUqsncFKlRDRs7ibC5TyHGPLqyt4yLZ95ibAU12NE6H6bZKMH3Etz0tU5obmP84u3hhEGkZttIuXcFUIq30tgg/0?wx_fmt=jpeg"}, {"title": "3 个令人眼前一亮的 ChatGPT 项目，开源了！", "source": "https://mp.weixin.qq.com/s/4NUHPUlv5NbI_KD-NAXjAg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/uDRkMWLia28iabk0OVtssIRVT9cyibsX9lykZY2NHCgz1fd5kd60e6AdzgoN5iavfvUtNnJiaKMukfvQ46bbAYztuTg/0?wx_fmt=jpeg"}, {"title": "使用 Android Studio 模板提升编码效率", "source": "https://mp.weixin.qq.com/s/jagaEj1wSiXe4Sj1InR8JA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKr8uassiaRbYmHBLbkG1quGunCeDY3JXaicBcLJl4bC5k6rFK1VOVTycqnlyIkOic1ribeSrVEcibgFF9w/0?wx_fmt=jpeg"}, {"title": "Gradle 全面拥抱 Kotlin！一起学起来！", "source": "https://mp.weixin.qq.com/s/CCkdAHoI3y9ot30KKPnsGw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/TeONTvjrLD7udlHcY1KU9X2icftgvyNs5pAyKtIEdxKhfeoueqnu5C6wzxGIgAs2rWU14250E4bbib7DTM4MkxZQ/0?wx_fmt=jpeg"}, {"title": "这些重要的 Fragment API 均已废弃，尽早替换起来吧～", "source": "https://mp.weixin.qq.com/s/QWBAonZ7bQra35nSBfbihw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/GIUVCiacpxgPrbHk7FeaASfrj9B7NzAeOVs6TfESL0SZIn5ia5nl2KLGzsd6vGrsN1FSbhBSZsByAwrkVQjTRVhQ/0?wx_fmt=jpeg"}, {"title": "打造高性能应用，持续优化用户体验", "source": "https://mp.weixin.qq.com/s/U9A-7jU5QKOI4rJtpC7Mew", "tag": "android", "remark": ""}, {"title": "Android 资源大汇总", "source": "https://mp.weixin.qq.com/s/qHQxoebCYoU3ZG7d5cti7A", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/MOu2ZNAwZwOUVs1lkMia4xiblpMCpnVs91tCHc1r2ibribqKZNxZZbyhviaEZCzxP6XqjQSzWYauSib1rDqic3RHAqf9g/0?wx_fmt=jpeg"}, {"title": "Kotlin | 这些隐藏的内存陷阱，你应该熟记于心", "source": "https://mp.weixin.qq.com/s/UmBIVXqKXrjtzSdmUn41pA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt7nXIsMdlgBMOvzo4qMwtSLO97JCdHibNNWbGmqcA0PzrBWWTC9cCSF1pU8U5EbQicf7ElHpWJfPs2A/0?wx_fmt=jpeg"}, {"title": "Android 仿朋友圈EditText中插入#话题能力", "source": "https://mp.weixin.qq.com/s/SuvRPnKhJd0OL3vskZieOA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/MOu2ZNAwZwPT2sXXWCVKlBa4fVxAFYXWqMgHIbGKfBCMZlibkKLLkuW9xCYZtGrrrYTIK4bDNLUIHsh2VJL2FBw/0?wx_fmt=jpeg"}, {"title": "WebView 经历的各种干货方案分享", "source": "https://mp.weixin.qq.com/s/lR09lszCULIvnsH30NzIjQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/MOu2ZNAwZwNHFWu9yuNIHfRbB51C8uQqB84QJIbHQayuAoDRTsiaGmveYhl5HkkHdJGkheRhXhp0hKRarB5ktgA/0?wx_fmt=jpeg"}, {"title": "实战 | 如何利用 Scrapy 编写一个完整的爬虫！", "source": "https://mp.weixin.qq.com/s/Jztp-OjBk0YzQZPCbfNGBQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/atOH362BoytL3efjAibOrCQbGetK8zEGEPAAE8LVuiaictwSJ64p7SItQKHr4s1lbp2cBESvxkvFORFm82UWQ8efA/0?wx_fmt=jpeg"}, {"title": "深度解读 Android 官方的模块化方案～", "source": "https://mp.weixin.qq.com/s/ntcF_sFLiaAHOVUB-eZPNg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/GIUVCiacpxgPsCRzjCjvaGLzEMI7URlb8UBPR4B9z8WXMyr9sa19YDqGqCkkWA0fGKoZNn9iaFUFl4YjibWcm8YzQ/0?wx_fmt=jpeg"}, {"title": "旧安卓机别扔了，自制 Web 服务器了解一下！", "source": "https://mp.weixin.qq.com/s/i-FBsFUJAZGnDZ8XLMq-Rg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/oGp3ImQqDob8GhCiaZv5BYWwHFibtiafA4zCU2FQtKQ2xc10MznY4ZZGQicMh34xelnoM97PaOqNia2oYajILzLYc2g/0?wx_fmt=jpeg"}, {"title": "送你几个用起来很爽的 Studio 插件", "source": "https://mp.weixin.qq.com/s/1nwj-cSW7Z-MLZOg4Ry6OA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/MOu2ZNAwZwPmp9NibUz4rfMcIcMDA9ebpV7OR3l8b7ib1UxLfnXVcHKicPCicuiacHU0cdEHSicGGKhoGl5GkgFHQMew/0?wx_fmt=jpeg"}, {"title": "遵循 Google Java 规范并引入 Checkstyle 检查，代码写的像诗一样！", "source": "https://mp.weixin.qq.com/s/UYRVHzj4P5QI4Zl3yVcnLQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/eQPyBffYbueTvicEJ5c5YiaST38giaQo5ViadhIPpmNRluovz8icaSFibribic7ibm4A2oGTCeAxP5JQRtO3iap0XcmZVHpQ/0?wx_fmt=jpeg"}, {"title": "封神之作：极致包体优化，解决 17 个业务痛点", "source": "https://mp.weixin.qq.com/s/NiV51jOeCnTRgo-TLFsw7g", "tag": "android", "remark": ""}, {"title": "3 个骚气满满的 ChatGPT 开源项目！", "source": "https://mp.weixin.qq.com/s/k3BLfajag0m8g-Q33T2KUA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/uDRkMWLia28g1Glc010V3rJO1lQNicaicD5jGvJP09V7Mmo5ibREcOOHFkib3e5zenGj388o9N92GpCP9iax7SHylKUQ/0?wx_fmt=jpeg"}, {"title": "谁在靠AI绘画“搞钱”？", "source": "https://mp.weixin.qq.com/s/tGoubOk5Hb5GYxhhgwb6iQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/03KNO9Fib2w4ZzrWcwgXLkceUODT0zE9HHOh2SK8YzdquPPEgmdGjgurian2TSAMIZwP59lAeetS2qNbH5E1g1tg/0?wx_fmt=jpeg"}, {"title": "Python 爬虫从入门到放弃（11 个有趣的 Python 爬虫例子）", "source": "https://mp.weixin.qq.com/s/BW1MhJQC1xDJJjYAG0RojQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/zRiam9B2qkhSWJySicKFl89qp9BlM1ZoCbBMA84tpXPlgLSBagSGFmbuw7bBokibEiagvgPAtyHUlywIdX6awaNVPQ/0?wx_fmt=jpeg"}, {"title": "谈谈对后台登陆页面的渗透测试", "source": "https://mp.weixin.qq.com/s/rPc1h6dqXsL26y6qYSLu4Q", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/BwqHlJ29vcoxPOUiaZUFzKaWoPtvBucFENmoPd0GDLFTYlDjwyoJAEhrVYzCjB5ozfSCYOJmXYTGJ14RdWACp7w/0?wx_fmt=jpeg"}, {"title": "史上最全Windows安全工具锦集", "source": "https://mp.weixin.qq.com/s/2rxFQ1I1cpzpMu5drINNCw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR38ArhF52SCzb3CibTj9VP4kp3CdsnPSKwN8icDHNUSic4lG0HHoPAZYbSP8j70P8peb2iag3SFicdXls8w/0?wx_fmt=jpeg"}, {"title": "装上这14个插件后，PyCharm真的是无敌的存在！", "source": "https://mp.weixin.qq.com/s/NspTRG7sErk2KjNV9K6VIA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/y0SBuxfLhalibfcYXCWooqVOmbWKPL1axFbFhWngndyibbxPRP6tvmvy7xJuDr8jdT1BIVoakIcxuiaIu0HNhibhyg/0?wx_fmt=jpeg"}, {"title": "【技术分享】安卓逆向面试题汇总 技术篇", "source": "https://mp.weixin.qq.com/s/Xx5dt5iH7DdaxWsggchrOg", "tag": "android", "remark": ""}, {"title": "Chrome Tools花式玩法（三）", "source": "https://mp.weixin.qq.com/s/aEZBVeeoBpDLxQNaWMdvIw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/sz_mmbiz_jpg/VIc6GqGL2Xa9xbYfvM0IO7vX9Y6ZjgOzpBUuFcgcxyAWJGfjHmicsv6lvP7j961CS1TmVfLxKfdibXEf05kyRNMA/0?wx_fmt=jpeg"}, {"title": "Jetpack MVVM七宗罪 之三 ：在 onViewCreated 中请求数据", "source": "https://mp.weixin.qq.com/s/Q6K-pvbEptMkwCc6dV1TMQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKqCSRhJDG85Urj4ZakK6TVJAOCBfxvdSKN3QqRAbGkZ6uwvCuAkSuSe6g6cE8KlNGfjELbokk73zQ/0?wx_fmt=jpeg"}, {"title": "APP脱壳技术|安服仔是如何在不会逆向的情况下脱壳的", "source": "https://mp.weixin.qq.com/s/0Z8yr_2snnhXx6rFTu6lCQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/DSdxibfK1G95lNTvSS1qQdWKWoxO8eKpgAcK7ThOM5rPFKT3ATUxIYZPsydrhdBORR0eicXbD9icTWTC5z8coxibzQ/0?wx_fmt=jpeg"}, {"title": "Android 12 已来，你的 App 崩溃了吗？", "source": "https://mp.weixin.qq.com/s/yuo7GYEmWVBV3sw-CA_hQw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/ZFbd8icuQe1IDjtp3ydK0c8RKFYPy5QOiaMicvBFaRuUrKTydRzNVcxELLkeNkic65EkBXQibhspqRFzeVF2Y4qrGww/0?wx_fmt=jpeg"}, {"title": "用更优雅的技术方案实现应用内多弹窗效果！", "source": "https://mp.weixin.qq.com/s/i_cnHyqrKRxgL5AaUlmTDg", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/QFjUqsncFKmkXicFMBugxNeyVwAEUWHIZqW5FuoUyTJpV4jmAzcxqKe8SHDP5icIUDCMIiaJicRR0EUqs7SXiaictibZQ/0?wx_fmt=jpeg"}, {"title": "<PERSON><PERSON><PERSON> Flow全面掌握，操作符都在这了！", "source": "https://mp.weixin.qq.com/s/1zlN0vSojJ_oe-2Ywb2A8Q", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/MOu2ZNAwZwNW0iaaic8gFKzDuOh5LGgKueRUaNmibqkDLCMUx4NuwElbACSOPrBAe6NY9Tvp0iaIHYpAOjia8e77Svg/0?wx_fmt=jpeg"}, {"title": "一学就会：Kotlin Flow 实战举例", "source": "https://mp.weixin.qq.com/s/Ni2HxzlhrbxT9bFdRMSf9Q", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKpRnzAptU5j6U2vxo4icMuQZLlS0cOgPic6uRmpNZUVxPgjdtEy3SzsKVqhL7bBOwfc9Nfn42PxlkuA/0?wx_fmt=jpeg"}, {"title": "Android安全开发编码规范", "source": "https://mp.weixin.qq.com/s/B56fgPwQbU6-ZfGT-KePxQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/LtmuVIq6tF0h0ZWBJkCqfhGB53xVHrq8I6GwZsib8fFbFlfOVYzj2icF1Emn9W4YqDXLVmZe3CpsM3IQyVelyWPA/0?wx_fmt=jpeg"}, {"title": "如何设计 API 接口，实现统一格式返回？", "source": "https://mp.weixin.qq.com/s/w0QEWVLh0tfGaGKDumwZug", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/eZzl4LXykQwMys2xLBicPSKXcJowIibZC1eGJ9a13sqibfP5NWyU5Qp9TydibHngiaBu6r0xhTH2GfEYqqUSqREd4bA/0?wx_fmt=jpeg"}, {"title": "干货 | Android HTTPS认证的N种方式和对抗方法总结", "source": "https://mp.weixin.qq.com/s/DY8JIj9hZsbDgZvIrCca5Q", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/3xxicXNlTXL8PaKBss6J1Nabt5ibtUDmDFP0wLuQ79Hn0jm0JvClJEWvXNNhMY0gD3Vk4urxXpRGZGCThXpBqsWg/0?wx_fmt=jpeg"}, {"title": "Android应用安全解决方案", "source": "https://mp.weixin.qq.com/s/I1YKGUC1u0oKeDoaq27iWw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/yyLvy204xWicnmdWz7VqWf524hoOibOB3zxeYOyic2Pz76k8NMw4s11HbQib51Zsibiafprp5m9drDEKiavJ6ccAKgslQ/0?wx_fmt=jpeg"}, {"title": "分享一个Kotlin 高端玩法：DSL！", "source": "https://mp.weixin.qq.com/s/TTq709cPl8rMW7ipMF57_g", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/MOu2ZNAwZwOqHNEmo0RWe5QUd6IoVukURR1mR3sEoXib6rZkrr9ZaSqHicH0g9x4rCkZBdhuVUdjiabQytKVPAia8w/0?wx_fmt=jpeg"}, {"title": "写个小 Demo 学习 Kotlin Flow", "source": "https://mp.weixin.qq.com/s/gXEeL7hVUR6ojCipsRADQQ", "tag": "android", "remark": ""}, {"title": "Android 组件化架构设计从原理到实战!", "source": "https://mp.weixin.qq.com/s/t_h2UXcOcI8quwzavsednw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/QFjUqsncFKmoxaqcehx9OhCcuWQkX0FiaEoLUttMuMBNY87QiaKbbaarwVscY6p6zl8KkTqmAXE9APJUVjeFibOGQ/0?wx_fmt=jpeg"}, {"title": "浏览网页就能泄露手机号的小秘密", "source": "https://mp.weixin.qq.com/s/_bTunF9TXBUze8ewCM--Fg", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/cOCqjucntdEblb7H0wMib1OTO621YXMuWol69PJ3N6ssL4DVuKD013IRoQWBavfLfzWoZ8UADDicbZhFcGec5HMQ/0?wx_fmt=jpeg"}, {"title": "手机淘宝项目中RecyclerView快速精准锚定的解决方案", "source": "https://mp.weixin.qq.com/s/Wxdyifcwyck4pj0rZGUQhw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/33P2FdAnjuibUpuH3bw0PWv4t6icYLp9IJT79aBB9k95YFf3uBxyX9opx1icKQ24MNYsiaac3hhNxb05eCibBBibx7cQ/0?wx_fmt=jpeg"}, {"title": "让这些优秀的Android开源项目大幅提升我们的工作效率", "source": "https://mp.weixin.qq.com/s/FUe_q3qPimSZ8H7GPTXp6A", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/DpbaFMrf4epMp3VrQjxZeN36he5nHpLQicjRRJFcs8J25jEWWb5XDN3ic3w2iam21VwKyusADRDEdXhteKAZXDmQQ/0?wx_fmt=jpeg"}, {"title": "应用架构指南全新发布", "source": "https://mp.weixin.qq.com/s/NBQ8h40y6AmQdNF2lEGKZQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/icFp8MFO4IKx79TVyQkdOtiacNpicqRUEiaMeHnS2cmPBm06XSdOnibT2g9gBDGSjJRJhX41wIQiaAqfbKRWqsW4f7cg/0?wx_fmt=jpeg"}, {"title": "优雅整洁的 Java 代码命名技巧，风之极·净化", "source": "https://mp.weixin.qq.com/s/o8UyXmOOlezQriYASvOIZw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/EoJib2tNvVtdsxhk6iazHH8Yfoyz95eLhOHb74jkJiauHV56FB16HqD8CF6Tb3n2DsSYNp14zwAeiaoXicpYw7QJ5Rw/0?wx_fmt=jpeg&random=0.4798475110540885"}, {"title": "学会 IDEA 的这个功能，阅读源码简直太简单了！！！", "source": "https://mp.weixin.qq.com/s/f3unZvveULhEahaJgbIqzA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/x0kXIOa6owWdQtZAltSNKqmTEAsrWungpx53MqInXZSRcfSZOV43t4BRYibqOkaCrMEFcFRYibCPK3h5WicscQsSg/0?wx_fmt=jpeg"}, {"title": "Mavericks：Airbnb 使用这套框架减少了 50% 代码量", "source": "https://mp.weixin.qq.com/s/cW9IGLKa_WL0dLrQk3d6Sw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKojhAeoNdbpLKViaRsH4SNd9dJBKoAlN1VNViaLljw4LN1rhb0KPrpYXwHXzqpb4tqIH1oBpSdrYa3g/0?wx_fmt=jpeg"}, {"title": "Android Studio 小技巧：使用 Bookmarks 和 TODO 提升开发效率", "source": "https://mp.weixin.qq.com/s/StHftyQTREx2rqUD3vyxZA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/sOxAruHOzKqZdskiacib0NkTJdibWkuaTAScjTcs1wD8J1yfLKWQkAGIqSEV3YcuRVNN6UV68M2BBr3Zm5WoaTiaEQ/0?wx_fmt=jpeg"}, {"title": "如何保护 Android 应用程序的 API 密钥", "source": "https://mp.weixin.qq.com/s/-V-kDucQ9xenogMKSxFthQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/7G6wAxO5rWAcDqBIbaf53swXa1mtH8W1ISfQn0xY39bT1TfibA1NP7E1SMDb4eRVaTgNdqqXbn7LycXpe3iaAJ2Q/0?wx_fmt=jpeg"}, {"title": "提升 WebView 用户体验的关键：Android WebChromeClient 解析", "source": "https://mp.weixin.qq.com/s/HRcMIi56lQNsz_iXmLLRXQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt77icNQYEx9puftOwwdKRMPweOTcYibXR4BddDOqRyJYQq5BaD0pNU8FCnSYF4vfLtJpYiaibUiadzhmzQ/0?wx_fmt=jpeg"}, {"title": "使用技巧：手把手教你开发一个Android Studio插件！", "source": "https://mp.weixin.qq.com/s/wsTnUBNKlQOtC53VxbV0Fw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/vEMApYVjEzeql28cicwoiakrkkxPhjRVCKFh0tjamfK4Gzic82Ku6Iq7S8paawQ4dOfXb93GqvBs5clnIo6PdZQiaA/0?wx_fmt=jpeg"}, {"title": "好心封装工具类，结果被领导怒怼：“会不会写代码！”", "source": "https://mp.weixin.qq.com/s/ITdFyH093BOrQ4_IH_rSiA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/y5HvXaQmpqmJYOYNQs0ATKzYUmKAtzicOBWvEAq7OicEwfN0HNG3UiaP5eBQfDUVrlIynuiaY3JKjicl6d2r4nv6Qhw/0?wx_fmt=jpeg"}, {"title": "【WEB漏洞挖掘】网络信息探测基础篇", "source": "https://mp.weixin.qq.com/s/BAMixf9kamLZBHJEAGSoYw", "tag": "android", "remark": ""}, {"title": "深度解读Jetpack框架的基石，AppCompat", "source": "https://mp.weixin.qq.com/s/rFpYjNgghI93ilENABRjBw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/v1LbPPWiaSt6A5XcqN4XQuvicgY2p1HhGYtVd2DBewL2QkkWxWD9KRga3xv1vdYdhN9Hh3vxTyWZy3wngEHboRmA/0?wx_fmt=jpeg"}, {"title": "这个方便又好用的 ChatGPT 客户端，开源了！", "source": "https://mp.weixin.qq.com/s/xfOs0coMTWxNPxhg9dO3cg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/uDRkMWLia28jYEEApGXfDL0mOMGnPliaAkhCtkMG25htRpPUyaTYYicISfqkiaRN04XqycbmUZ3STn9Vf2c1WfDFzQ/0?wx_fmt=jpeg"}, {"title": "国庆在家没事干？教大家用Python做一个任何视频都能看的软件， 当然，只能看正经的", "source": "https://mp.weixin.qq.com/s/W9lZrQ44ipV4L9C_4Cynaw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/pOTh2wdMWXp8a6x4Ts3iaicwZjdpOOc8STGHXOtZn8F6Y3XBMlXx75qWjtO1RH9WtyPicFNoNd6OzOQ2wYTuKaOxw/0?wx_fmt=jpeg"}, {"title": "中国码农，35岁后出口海外", "source": "https://mp.weixin.qq.com/s/EyJeqb2S8eW0YQHUN5fPRw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/b2YlTLuGbKB3YiadIvlA3k0cfg4MLqxGjGaribicDDibLzEia20HjJySiawHo9x9HQHzu1QOfmJQxGYH8Hd2rOm1Bib4Q/0?wx_fmt=jpeg"}, {"title": "用Python破解WiFi密码，太刺激了！", "source": "https://mp.weixin.qq.com/s/5WsVvr-VOFtisA2AZtc_cg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/4J9DtZ0rhjCamku1xeQpNARY7TEIpVLLZaCSgp6f0wyCpUInqdicKNof6lummEqZf1cyOQqoaHpmFAPGjUeMy8Q/0?wx_fmt=jpeg"}, {"title": "关于pycharm打开时很卡，一直加载中的解决办法~", "source": "https://mp.weixin.qq.com/s/LHP4nqmRbZ0AcURQjq6TAw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/pOTh2wdMWXrtBxwwq9OCUoHS8bsax3yLPx7vO4Nam1XxicydLSXSRuq2alKscows1NicU7BtWhXL4fb9Xx3TgOag/0?wx_fmt=jpeg"}, {"title": "AndroidMonitor抓包原理及使用方法", "source": "https://mp.weixin.qq.com/s/Bf6TP2i_UkNM0uxI2kPsZQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/LtmuVIq6tF1q5Ibeziah95yS0pL5WACR9zcW3pzPkL9tEtKZia9ibC99Dn7qXWENKjfePIVXD3y0fmNoFPxueaEuQ/0?wx_fmt=jpeg"}, {"title": "你真的了解 OkHttp 中的 复用连接池 吗？", "source": "https://mp.weixin.qq.com/s/bsJCGssd1r_Y268rRPG9dQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/nk8ic4xzfuQ8Rfjg46ziaGMRgEruTh19DYdu6upbOHibJ8hZM0aPOia78dHT8eLbAslZx20icHhRHhPMkOHicXAOica2A/0?wx_fmt=jpeg"}, {"title": "Android Webp 完全解析 快来缩小 APK 的大小吧", "source": "https://mp.weixin.qq.com/s/r2E19zZVQntBHDt6i9z8GA", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_png/MOu2ZNAwZwMmeya9Ywoic9Evf23NbDCia7S6m0RIjjsGw6HbqRxF8Z0WT8qP1K7VfUc8Y4XxiaAt6ICTgebSRhqZA/0?wx_fmt=png"}, {"title": "Android 官方开源App，来看看最佳实践吧", "source": "https://mp.weixin.qq.com/s/CO2PWMm_q98so3k4Q0hl1A", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/1o1jRN50x9OVzUaJ0XAysCONPaFoLfdo2Bk6mfBxDpMCLk9nluGqTR3dYwe5OD5nLTk1ibEicYPsO62OGuBqqbVA/0?wx_fmt=jpeg"}, {"title": "Python基于PC版微信实现机器人", "source": "https://mp.weixin.qq.com/s/ynEP5IZZBTPlw6ZWsm96-g", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/jEq8oqHMgSE8NTXibBcUDLALDkRLEX2cU7o2BQ8lQBNdW951Ut2nic0SiamtPMvokImtmbIbIbOPjuQzEWEqFJicfg/0?wx_fmt=jpeg"}, {"title": "聊聊几个 Android Studio 高效开发小技巧", "source": "https://mp.weixin.qq.com/s/4KV5Z5UlJ82mt0xHeEYBNw", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/QFjUqsncFKmB1eatgJJfDVan6PQNvhMUq5ibmVZ1r1jlxCoKtYgPXLvrDC1IaMuiaibb9lD1Ig9gI2TUkpHSEDp2g/0?wx_fmt=jpeg"}, {"title": "Python 下载大文件，哪种方式速度更快", "source": "https://mp.weixin.qq.com/s/YQ1yLTAkAexrqK9TNaK8MQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/l7USgGbZu1xa3YZibx0jwZH92NvIF2GAXgSibj94c48SduEWF1nW4Yl9WAiawptMhKqzk6eRSntk5N7rv5lkvEMaA/0?wx_fmt=jpeg"}, {"title": "我的师父把 「JWT 令牌」玩到了极致", "source": "https://mp.weixin.qq.com/s/MVImd2rrXg4AEJy3GxM8vg", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/SfAHMuUxqJ120NTDIRqe1rr9ZuYckIKaltpEfOLCBb8INiag33l6lfnq7UFXbYSdO0FZbRqK920PTibbdoKdoNQw/0?wx_fmt=jpeg"}, {"title": "一个更优的 RecyclerView 添加 onItemClickListener 解决方案", "source": "https://mp.weixin.qq.com/s/4Owlws0-b71N3ALcd_rCxQ", "tag": "android", "remark": "", "cover": "https://mmbiz.qpic.cn/mmbiz/MOu2ZNAwZwOnjbibqZUaTr2aSG36hNj5pgXQQdzDhMib1x4E9tNtjqR0OjYlllRTdbNseyTPF8lu634et8AqbPYQ/0?wx_fmt=png"}, {"title": "手敲代码太繁琐？“拖拉拽”式编程惊艳到我了", "source": "https://mp.weixin.qq.com/s/QTjgokzownh8EFPN8TtwNQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/bngAobveWGe4qicIzmPUmym2ibGUddFM0gFibU55ruVeGIfxcCZoiaRODBxdDc6DZFIXTMAicicBia0AP8bfIU5DrR4MQ/0?wx_fmt=jpeg"}, {"title": "你需要了解的APP安全", "source": "https://mp.weixin.qq.com/s/k5LP9a69hwEDGFBeOE3biQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/jVCRndy8Lr4fbs5TmjLueWRJTRM9kXlkq3qaeMDVp8hSoMM3vdE5sLFwz0hA0l1IqJyYX11iatOQnPCnuGX94Rw/0?wx_fmt=jpeg"}, {"title": "Google 还发布了这个库？ 告别shape、各种 drawable...", "source": "https://mp.weixin.qq.com/s/z3FkJHTSkH_TFis_VOdkSw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/MOu2ZNAwZwN5UomAwicwLAb6zMA2FyMkd8V1syphLSia1gjqNlnKEcJoZkYvbTg2hlScbTn93d0znYiaCAYKBjyWg/0?wx_fmt=jpeg"}, {"title": "黑客 如何攻破一个网站？长文图解全流程", "source": "https://mp.weixin.qq.com/s/Rz1nvVTfNNd8RGrtIdc1nA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/bngAobveWGdKzibKDs3iabebBT86gelUibdDwicFbS1maKjia4Vbf7QRsk5ZJVnlLsrcRKArT7ZSTJwic7k7bIf9hMOg/0?wx_fmt=jpeg"}, {"title": "推荐一款 IDEA 神器 ，人工智能帮你写代码，再也不用加班了！！", "source": "https://mp.weixin.qq.com/s/yynYp1wSfpqM2eWAMuV97Q", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/TNUwKhV0JpSjpzg4SDibkQ6icwHvf2iaJVn2lUg1yAPFAxHHaeibt0Pzr6GBE8sBtKNmStibCjpcazID6smP8iajqtiaQ/0?wx_fmt=jpeg"}, {"title": "终于把所有的Python库，都整理出来啦！", "source": "https://mp.weixin.qq.com/s/XDo6oB9asmD8MRwE61SnNQ", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/MQ4FoG1HmnK1aUbIzicBpNJaxx9l3Vr28tib1jwPCQzFS055yQ22TSejozMEdcqDYKHPiaqLdF8lgYybAQrm5yRcw/0?wx_fmt=jpeg"}, {"title": "用Python分析了5万条相亲网站数据，看相亲男女画像", "source": "https://mp.weixin.qq.com/s/PWVT3E3859KJrp7gNDNaIg", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/Nl0MnEkG9FL1GFsibdicicia2BpHobhve4eY1J99UW7cZxd9DlMKrIfV3KEb1HxMHow0S6vxjIsDu3YX4XH64152yg/0?wx_fmt=jpeg"}, {"title": "奇思妙想之用JS给图片加口令", "source": "https://mp.weixin.qq.com/s/gRg8yCUtEX5maLm3wAAwBg", "tag": "android", "remark": ""}, {"title": "我的信息搜集之道", "source": "https://mp.weixin.qq.com/s/XGdeoQKjOkUFpc6HV3IfGw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/qq5rfBadR3icW5xUrfVrRh1aryJoru9pTTU6ibNZrd9IVIbyibovqjxicXz5jD6adAkOQhThvVkX3rINqdAaldOkGQ/0?wx_fmt=jpeg"}, {"title": "左耳朵耗子：如何通过技术和技能赚钱？", "source": "https://mp.weixin.qq.com/s/qGSVNFh6TP9Tosoapvn6sA", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/ibnDKD3ktAoYATgSOT943u7vd1icnnMEsuEODC8HOEVmk4C2yjSPhZZZvKDCSSKerVkhSXlI1v7Hh1Xf1diaXjFaQ/0?wx_fmt=jpeg"}, {"title": "如何通过一张照片来获取ip地址？", "source": "https://mp.weixin.qq.com/s/LY73EwjWpxKCelg098tZWw", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz_jpg/5kkib9oAJf2aib2WorGn7E3OJAwPbq7WotYfccKEeJnexapDv0ayM6RgVich1ic6ZiceBWiaZ2FDwE9KqIib0ibAAToEHA/0?wx_fmt=jpeg"}, {"title": "Anonymous手把手教你20步黑掉20%的暗网网站(含下载)", "source": "https://mp.weixin.qq.com/s/o9PwLxs9-eYwM_JvCsWucw", "tag": "android", "remark": ""}, {"title": "如何高效阅读计算机类图书？", "source": "https://mp.weixin.qq.com/s/00ec3XhEgawy-UtJ2kqk5g", "tag": "android", "remark": "", "cover": "http://mmbiz.qpic.cn/mmbiz/qf3SsyYtqBM2zRSfpVK2Z7yV65jlhZHr99wicI5g3eSYvQtWlEyqq89V49PIYQ6S3ibofSAdyaICuwQFsKm95e2Q/0?wx_fmt=jpeg"}]