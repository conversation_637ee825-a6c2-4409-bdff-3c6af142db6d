import json
from collections import defaultdict


def data_treating():
    """查找并打印wechat.json中的重复数据，最后按时间排序"""
    json_path = 'wechat_favorite.json'
    try:
        # 读取JSON文件
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 使用字典来存储标题和对应的条目
        title_dict = defaultdict(list)
        source_dict = defaultdict(list)

        # 遍历所有条目，按标题和链接分组
        for index, item in enumerate(data):
            if 'title' in item:
                title_dict[item['title']].append((index, item))
            if 'source' in item:
                source_dict[item['source']].append((index, item))

        # 打印重复的标题
        print("\n=== 重复的标题 ===")
        for title, items in title_dict.items():
            if len(items) > 1:
                print(f"\n标题: {title}")
                for index, item in items:
                    print(f"索引 {index}: {item['source']}")

        # 打印重复的链接
        print("\n=== 重复的链接 ===")
        for source, items in source_dict.items():
            if len(items) > 1:
                print(f"\n链接: {source}")
                for index, item in items:
                    print(f"索引 {index}: {item['title']}")

        print("\n=== 按时间排序 ===")
        sorted_data = sorted(data, key=lambda x: x['publish_time'])
        print(sorted_data)
    except FileNotFoundError:
        print(f"未找到文件: {json_path}")
    except Exception as e:
        print(f"发生错误: {str(e)}")


if __name__ == '__main__':
    data_treating()
