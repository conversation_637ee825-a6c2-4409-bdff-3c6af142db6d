import json
import traceback

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

driver = webdriver.Chrome()  # 请确保您已安装 ChromeDriver 并将其添加到 PATH

url_list = [
    "https://mp.weixin.qq.com/s/mnmJwySI7kjCKukbZaKd3A",
    "https://mp.weixin.qq.com/s/O8IPdG2E4bCXVR6NQicDNg",
    "https://mp.weixin.qq.com/s/ygatcD9hQSPdpnkdRgFupQ",
    "https://mp.weixin.qq.com/s/zX2V_Fc73nImnzdAzBB_oA",
]
try:
    for url_item in url_list:
        driver.get(url_item)

        title_element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h1[@id='activity-name'][@class='rich_media_title ']"))
        )
        title = title_element.text

        meta_element = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, '//meta[@property="og:image"]'))
        )
        cover = meta_element.get_attribute("content")

        publish_time_element = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.ID, "publish_time"))
        )
        publish_time = publish_time_element.text

        result_json = json.dumps({'title': title,
                                  'cover': cover,
                                  'source': url_item,
                                  'tag': '',
                                  'publish_time': publish_time,
                                  'remark': ''
                                  }, ensure_ascii=False)
        print(result_json, ",")
except Exception as e:
    print(e)
    print(traceback.format_exc())
    print(f"发生错误: {e}")
finally:
    driver.quit()  # 关闭浏览器
