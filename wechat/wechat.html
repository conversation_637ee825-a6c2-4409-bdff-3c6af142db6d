<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>文章列表</title>
    <meta content="never" name="referrer">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .article-list {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .article-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
            display: flex;
            gap: 20px;
        }

        .article-image {
            flex: 0 0 200px;
        }

        .article-cover {
            width: 100%;
            height: 120px;
            object-fit: fill;
            border-radius: 4px;
        }

        .article-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .article-title {
            font-size: 18px;
            color: #333;
            text-decoration: none;
            margin-bottom: 10px;
            display: block;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-title:hover {
            color: #1890ff;
        }

        .article-tag {
            color: #666;
            font-size: 14px;
        }

        .loading, .error {
            text-align: center;
            padding: 20px;
            font-size: 18px;
        }

        .loading::after {
            content: '...';
            animation: dots 1s steps(5, end) infinite;
        }

        @keyframes dots {
            0%, 20% {
                content: '.';
            }
            40% {
                content: '..';
            }
            60% {
                content: '...';
            }
            80%, 100% {
                content: '';
            }
        }

        @media (max-width: 600px) {
            .article-item {
                flex-direction: column;
            }

            .article-image {
                flex: none;
            }

            .article-cover {
                width: 100%;
                height: auto;
            }
        }

        /* 添加搜索框样式 */
        .search-container {
            margin-bottom: 20px;
            text-align: center;
        }

        .search-input {
            width: 80%;
            max-width: 500px;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            outline: none;
        }

        .search-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
    </style>
</head>
<body>
<div class="article-list">
    <!-- 添加搜索框 -->
    <div class="search-container">
        <input type="text" class="search-input" placeholder="搜索文章标题..." id="searchInput">
    </div>
    <div id="loading" class="loading">正在加载数据</div>
    <div id="error" class="error" style="display: none;">加载失败，请稍后再试</div>
</div>
<script>
    const container = document.querySelector('.article-list');
    const loading = document.getElementById('loading');
    const error = document.getElementById('error');
    let articles = []; // 存储所有文章数据

    // 添加搜索功能
    function filterArticles(searchText) {
        const filteredArticles = searchText ?
            articles.filter(article =>
                article.title.toLowerCase().includes(searchText.toLowerCase())
            ) : articles;

        renderArticles(filteredArticles);
    }

    function renderArticles(articlesToRender) {
        // 清除除搜索框和加载提示外的所有内容
        const elements = container.getElementsByClassName('article-item');
        while (elements.length > 0) {
            elements[0].parentNode.removeChild(elements[0]);
        }

        articlesToRender.forEach(article => {
            const articleDiv = document.createElement('div');
            articleDiv.className = 'article-item';

            let html = `
                    <div class="article-image">
                        ${article.cover ?
                `<img src="${article.cover}" class="article-cover" alt="${article.title}">` :
                `<img src="https://via.placeholder.com/200x120" class="article-cover" alt="默认图片">`
            }
                    </div>
                    <div class="article-content">
                        <a href="${article.source}" target="_blank" class="article-title">
                            ${article.title}
                        </a>
                        <div>${article.publish_time}</div>
                        <div>${article.remark}</div>
                        <div class="article-tag">标签: ${article.tag}</div>
                    </div>`;

            articleDiv.innerHTML = html;
            container.appendChild(articleDiv);
        });
    }

    // 监听搜索输入
    document.getElementById('searchInput').addEventListener('input', (e) => {
        filterArticles(e.target.value);
    });

    fetch('wechat_favorite.json')
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';
            articles = data; // 保存文章数据
            articles.reverse();
            renderArticles(articles); // 初始渲染所有文章
        })
        .catch(err => {
            console.error('Error:', err);
            loading.style.display = 'none';
            error.style.display = 'block';
        });
</script>
</body>
</html>
