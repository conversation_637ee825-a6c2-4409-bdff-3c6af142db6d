[{"url": "", "description": "", "type": "", "category": "", "remarks": ""}, {"url": "", "description": "", "type": "", "category": "", "remarks": ""}, {"url": "", "description": "", "type": "", "category": "", "remarks": ""}, {"url": "", "description": "", "type": "", "category": "", "remarks": ""}, {"url": "", "description": "", "type": "", "category": "", "remarks": ""}, {"url": "", "description": "", "type": "", "category": "", "remarks": ""}, {"url": "https://github.com/clash-verge-rev/clash-verge-rev", "description": "基于 Tauri 的现代 GUI 客户端，旨在在 Windows、macOS 和 Linux 中运行，以提供定制的代理体验", "type": "", "category": "", "remarks": "vpn"}, {"url": "https://github.com/abbodi1406/vcredist", "description": "适用于最新 Microsoft Visual C++ 可再发行运行时的 AIO Repack", "type": "", "category": "", "remarks": ""}, {"url": "https://apps.microsoft.com/detail/9nzjndtlvkr1?hl=en-us&gl=PE", "description": "适用于 Windows 的最佳免费屏幕录像机。无水印、无功能锁、无广告。", "type": "", "category": "", "remarks": ""}, {"url": "https://www.bilibili.com/opus/399603138752365845/?from=readlist", "description": "装饰直播/视频/桌面的工具，它能根据鼠标、键盘、手柄操作做出相应的动作", "type": "", "category": "", "remarks": "Bongo Cat"}, {"url": "https://www.pdfgear.com/", "description": "跨设备阅读、编辑、转换、合并和签署 PDF 文件，完全免费且无需注册。", "type": "", "category": "", "remarks": ""}, {"url": "https://github.com/c0re100/qBittorrent-Enhanced-Edition", "description": "[非官方] qBittorrent 增强版，基于 qBittorrent", "type": "", "category": "", "remarks": ""}, {"url": "https://updf.com/", "description": "UPDF - 集成 AI 的 PDF 编辑器、转换器、注释器和阅读器", "type": "", "category": "", "remarks": ""}, {"url": "https://www.nirsoft.net/utils/wireless_network_watcher.html", "description": "无线网络观察器", "type": "", "category": "", "remarks": ""}, {"url": "https://get.adobe.com/cn/reader/enterprise/", "description": "免费 PDF 阅读器", "type": "", "category": "", "remarks": ""}, {"url": "https://www.softpedia.com/get/Security/Decrypting-Decoding/Advanced-RAR-Password-Recovery.shtml", "description": "一个简单的软件，允许用户轻松恢复丢失或忘记的 RAR 或 WinRAR 档案密码", "type": "", "category": "", "remarks": ""}, {"url": "https://github.com/OdysseusYuan/LKY_OfficeTools", "description": "一键自动化 下载、安装、激活 Office 的利器。", "type": "", "category": "", "remarks": ""}, {"url": "https://github.com/nilaoda/N_m3u8DL-CLI?tab=readme-ov-file", "description": "[.NET] m3u8 downloader 开源的命令行m3u8/HLS/dash下载器，支持普通AES-128-CBC解密，多线程，自定义请求头等.", "type": "", "category": "", "remarks": ""}, {"url": "https://diskanalyzer.com/", "description": "WizTree - 最快的磁盘空间分析器", "type": "", "category": "", "remarks": ""}, {"url": "https://github.com/localsend/localsend", "description": "AirDrop 的开源跨平台替代品", "type": "", "category": "", "remarks": "文件传输"}, {"url": "https://www.ieway.cn/", "description": "音视频软件", "type": "", "category": "", "remarks": ""}, {"url": "https://horsicq.github.io/", "description": "查看软件信息", "type": "逆向", "category": "", "remarks": ""}, {"url": "https://github.com/zhongyang219/TrafficMonitor", "description": "这是一个用于显示当前网速、CPU及内存利用率的桌面悬浮窗软件，并支持任务栏显示，支持更换皮肤。", "type": "", "category": "", "remarks": ""}, {"url": "https://github.com/zhongyang219/MusicPlayer2", "description": "这是一款可以播放常见音频格式的音频播放器。支持歌词显示、歌词卡拉OK样式显示、歌词在线下载、歌词编辑、歌曲标签识别、Win10小娜搜索显示歌词、频谱分析、音效设置、任务栏缩略图按钮、主题颜色等功能。 播放内核为BASS音频库(V2.4)。", "type": "", "category": "", "remarks": ""}, {"url": "https://github.com/BlackINT3/OpenArk", "description": "适用于 Windows 的下一代反 Rookit (ARK) 工具。", "type": "", "category": "", "remarks": ""}, {"url": "https://www.telerik.com/download/fiddler", "description": "抓包", "type": "", "category": "", "remarks": ""}, {"url": "https://recovery-software.ru/", "description": "恢复数据", "type": "", "category": "", "remarks": ""}]