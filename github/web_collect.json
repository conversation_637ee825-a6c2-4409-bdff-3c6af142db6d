[{"url": "https://github.com/linshenkx/prompt-optimizer", "description": "一款提示词优化器，助力于编写高质量的提示词", "type": "AI工具", "categories": "", "remarks": ""}, {"url": "https://github.com/harry0703/MoneyPrinterTurbo", "description": "利用AI大模型，一键生成高清短视频 Generate short videos with one click using AI LLM.", "type": "AI视频生成", "categories": "", "remarks": ""}, {"url": "https://github.com/punkpeye/awesome-mcp-servers", "description": "MCP 服务器的集合。", "type": "开发工具集合", "categories": "", "remarks": ""}, {"url": "https://github.com/ahmedkhaleel2004/gitdiagram", "description": "适用于任何 GitHub 存储库的免费、简单、快速交互式图表", "type": "可视化工具", "categories": "", "remarks": ""}, {"url": "https: //github.com/ionictemplate-app/SEO-Scrape-All-InONE", "description": "外贸营销推广工具-- 是希望能够快速轻松地从社交网络中抓取数据的企业的绝佳选择。 它提供了多种功能，使从社交网络中抓取数据变得简单高效，例如一次从多个网络中提取数据的能力。 此外，它还可用于从个人资料中收集电子邮件和电话号码，以及其他重要信息，例如 whatsapp 联系人等", "type": "数据抓取工具", "categories": "", "remarks": ""}, {"url": "https: //github.com/The-Pocket/PocketFlow", "description": "ocket Flow：100 行 LLM 框架。让 Agent 自己构建 Agent！", "type": "AI框架", "categories": "", "remarks": ""}, {"url": "https: //github.com/WEIFENG2333/VideoCaptioner", "description": "卡卡字幕助手 | VideoCaptioner - 基于 LLM 的智能字幕助手 - 视频字幕生成、断句、校正、字幕翻译全流程处理！- A powered tool for easy and efficient video subtitling.", "type": "视频处理工具", "categories": "", "remarks": ""}, {"url": "https: //github.com/NanmiCoder/MediaCrawler", "description": "小红书笔记 | 评论爬虫、抖音视频 | 评论爬虫、快手视频 | 评论爬虫、B 站视频 ｜ 评论爬虫、微博帖子 ｜ 评论爬虫、百度贴吧帖子 ｜ 百度贴吧评论回复爬虫 | 知乎问答文章｜评论爬虫", "type": "数据爬虫工具", "categories": "", "remarks": ""}, {"url": "https://github.com/srcbookdev/srcbook", "description": "以 TypeScript 为中心的应用程序开发平台：笔记本和 AI 应用程序构建器", "type": "开发平台", "categories": "AI code", "remarks": "AI生成项目"}, {"url": "https://viggle.ai/", "description": "AI视频", "type": "AI视频平台", "categories": "AI video", "remarks": ""}, {"url": "https://github.com/Shubhamsaboo/awesome-llm-apps?tab=readme-ov-file", "description": "使用 OpenAI、Anthropic、Gemini 和开源模型的具有 AI 代理和 RAG 的出色 LLM 应用程序集合。", "type": "AI应用集合", "categories": "AI", "remarks": "AI合集"}, {"url": "https://www.yescanner.com/", "description": "yescan – 扫描仪，免费 OCR 扫描和 PDF 转换器", "type": "OCR工具", "categories": "", "remarks": ""}, {"url": "https://github.com/1c7/chinese-independent-developer", "description": "中国独立开发者项目列表 -- 分享大家都在做什么", "type": "", "categories": "", "remarks": ""}, {"url": "https://github.com/sirioberati/WebTwin", "description": "Website Extractor 是一款基于 Python 的强大工具，只需单击即可下载并存档整个网站。", "type": "", "categories": "", "remarks": ""}, {"url": "https://github.com/myhhub/stock", "description": "stock股票.获取股票数据,计算股票指标,筹码分布,识别股票形态,综合选股,选股策略,股票验证回测,股票自动交易,支持PC及移动设备。", "type": "", "categories": "", "remarks": ""}, {"url": "https://github.com/TauricResearch/TradingAgents", "description": "TradingAgents：多代理 LLM 金融交易框架", "type": "", "categories": "", "remarks": ""}, {"url": "https://github.com/QwenLM/Qwen-Image", "description": "Qwen-Image是一个强大的图像生成基础模型，能够进行复杂的文本渲染和精确的图像编辑。", "type": "", "categories": "", "remarks": ""}, {"url": "https://github.com/test-time-training/ttt-video-dit", "description": "通过测试时间训练实现一分钟视频生成的官方 PyTorch 实现", "type": "", "categories": "", "remarks": ""}, {"url": "https://github.com/Budibase/budibase", "description": "几分钟内即可创建业务应用并实现工作流程自动化。支持 PostgreSQL、MySQL、MariaDB、MSSQL、MongoDB、Rest API、Docker、K8s 等 🚀 无代码/低代码平台。", "type": "", "categories": "", "remarks": ""}, {"url": "https://github.com/CherryHQ/cherry-studio", "description": "Cherry Studio 是一款支持多个 LLM 提供商的桌面客户端。", "type": "", "categories": "", "remarks": "AI知识库"}, {"url": "https://github.com/chatboxai/chatbox", "description": "适用于 AI 模型/LLM（GPT、<PERSON>、<PERSON>、<PERSON><PERSON><PERSON>……）的用户友好型桌面客户端应用程序", "type": "", "categories": "", "remarks": "AI知识库"}, {"url": "", "description": "", "type": "", "categories": "", "remarks": ""}]