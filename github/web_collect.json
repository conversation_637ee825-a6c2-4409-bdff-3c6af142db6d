[{"url": "https://github.com/linshenkx/prompt-optimizer", "description": "一款提示词优化器，助力于编写高质量的提示词", "categories": "", "remarks": ""}, {"url": "https://github.com/harry0703/MoneyPrinterTurbo", "description": "利用AI大模型，一键生成高清短视频 Generate short videos with one click using AI LLM.", "categories": "", "remarks": ""}, {"url": "https://github.com/punkpeye/awesome-mcp-servers", "description": "MCP 服务器的集合。", "categories": "", "remarks": ""}, {"url": "https://github.com/ahmedkhaleel2004/gitdiagram", "description": "适用于任何 GitHub 存储库的免费、简单、快速交互式图表", "categories": "", "remarks": ""}, {"url": "https: //github.com/ionictemplate-app/SEO-Scrape-All-InONE", "description": "外贸营销推广工具-- 是希望能够快速轻松地从社交网络中抓取数据的企业的绝佳选择。 它提供了多种功能，使从社交网络中抓取数据变得简单高效，例如一次从多个网络中提取数据的能力。 此外，它还可用于从个人资料中收集电子邮件和电话号码，以及其他重要信息，例如 whatsapp 联系人等", "categories": "", "remarks": ""}, {"url": "https: //github.com/The-Pocket/PocketFlow", "description": "ocket Flow：100 行 LLM 框架。让 Agent 自己构建 Agent！", "categories": "", "remarks": ""}, {"url": "https: //github.com/WEIFENG2333/VideoCaptioner", "description": "卡卡字幕助手 | VideoCaptioner - 基于 LLM 的智能字幕助手 - 视频字幕生成、断句、校正、字幕翻译全流程处理！- A powered tool for easy and efficient video subtitling.", "categories": "", "remarks": ""}, {"url": "https: //github.com/NanmiCoder/MediaCrawler", "description": "小红书笔记 | 评论爬虫、抖音视频 | 评论爬虫、快手视频 | 评论爬虫、B 站视频 ｜ 评论爬虫、微博帖子 ｜ 评论爬虫、百度贴吧帖子 ｜ 百度贴吧评论回复爬虫 | 知乎问答文章｜评论爬虫", "categories": "", "remarks": ""}, {"url": "https://github.com/srcbookdev/srcbook", "description": "以 TypeScript 为中心的应用程序开发平台：笔记本和 AI 应用程序构建器", "categories": "AI code", "remarks": "AI生成项目"}, {"url": "https://viggle.ai/", "description": "AI视频", "categories": "AI video", "remarks": ""}, {"url": "https://github.com/Shubhamsaboo/awesome-llm-apps?tab=readme-ov-file", "description": "使用 OpenAI、Anthropic、Gemini 和开源模型的具有 AI 代理和 RAG 的出色 LLM 应用程序集合。", "categories": "AI", "remarks": "AI合集"}, {"url": "https://www.yescanner.com/", "description": "yescan – 扫描仪，免费 OCR 扫描和 PDF 转换器", "type": "", "categories": "", "remarks": ""}, {"url": "", "description": "", "type": "", "categories": "", "remarks": ""}]