import json
import os

filepath = r"C:\Users\<USER>\AppData\LocalLow\S3Studio\House of Legacy\FW\5\GameData.es3"


def search_qu_data(keyword):
    """
    搜索GameData.es3文件中Member_qu对应的中文数据
    :param keyword: 搜索关键字
    :return: 匹配的数据列表和原始数据
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(filepath):
            return "错误：找不到GameData.es3文件", None

        # 读取文件内容
        with open(filepath, 'r', encoding='utf-8') as file:
            data = json.load(file)

        # 搜索Member_qu数据
        results = []
        for item in data['Member_qu']['value']:
            print(item[2])
            # 搜索人名
            if keyword in item[2]:
                results.append(item)

            # 修改数据
            # 声誉
            # item[12]
            # 体力
            item[20]='100'
            # 健康
            item[16]='100'
            # 心情
            item[10]='100'
            # 魅力
            item[15]='100'
            # 幸运
            item[6]='100'
            # 夫妻关系
            item[31] = "100|100"

        return results if results else "未找到匹配的数据", data

    except json.JSONDecodeError:
        return "错误：文件格式不正确，无法解析JSON数据", None
    except Exception as e:
        return f"发生错误：{str(e)}", None


def save_data(data):
    """
    保存修改后的数据到文件
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存文件时发生错误：{str(e)}")
        return False


def main():
    # 获取用户输入的关键字
    keyword = "凌可梦"

    # 执行搜索
    results, original_data = search_qu_data(keyword)

    # 打印结果
    print("\n搜索结果：")
    if isinstance(results, list):
        for item in results:
            print(json.dumps(item, ensure_ascii=False, indent=2))
            # 修改夫妻关系值
            print(json.dumps({
                '姓名': item[2].split('|')[0],
                '声誉': item[12],
                '体力': item[20],
                '年龄': item[5],
                '健康': item[16],
                '心情': item[10],
                '魅力': item[15],
                '幸运': item[6],
                '夫妻关系': item[31],
                '#':'#',
                '文才': item[6],
                '武艺': item[7],
                '商才': item[8],
                '艺才': item[9],
                '计谋': item[19],
            }, ensure_ascii=False, indent=2))

        # 保存修改后的数据
        if save_data(original_data):
            print("\n数据已成功保存到文件")
        else:
            print("\n保存数据失败")
    else:
        print(results)

# FamilyData 改钱
# Member_now 里面有家族人员
# Member_qu 里面的是迎娶或者入赘来的
# Menke_now 里面的是门客

# 第几代|
# 天赋类型1文2武3商4艺|
# 天赋数值|
# |
# 年龄|
# 技能类型|
# 幸运|
# 喜好|
# |

if __name__ == "__main__":
    main()
