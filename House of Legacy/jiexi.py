import json


def jiexi_Member_qu():
    Member_qu = [
        "M577",
        "12|13|2|15",
        "邓贞若|-100|0|0|0|103|3|17|8|M0|7|null",
        "null",
        "0|TMB15|null|5",
        "27",
        "100",
        "10",
        "1.580014",
        "15.16555",
        "100",
        "0",
        "10.97676",
        "1",
        "null|null|null",
        "100",
        "100",
        "null",
        "-1",
        "30",
        "100",
        "0|0|0|0|0|0|0",
        "1|2|0|0|0|0",
        "4",
        "0",
        "0",
        "22@82@您家族@伍衡|24@98@null@null",
        "null",
        "null",
        "1",
        "0@0@0@-1@-1|0",
        "100|100",
        "4|11|0"
    ]

    mod = json.dumps({
        '姓名': Member_qu[2].split('|')[0],
        '声誉': Member_qu[12],
        '体力': Member_qu[20],
        '年龄': Member_qu[5],
        '健康': Member_qu[16],
        '心情': Member_qu[10],
        '魅力': Member_qu[15],
        '幸运': Member_qu[6],
        '夫妻关系': Member_qu[31],
        '#': '#',
        '文才': Member_qu[6],
        '武艺': Member_qu[7],
        '商才': Member_qu[8],
        '艺才': Member_qu[9],
        '计谋': Member_qu[19],
    }, ensure_ascii=False, indent=2)
    print(mod)


def jiexi_Member_now():
    Member_now = [
        "M0",
        "0|8|1|8",
        "M404|M405|M488",
        "0|TMB15|null|5",
        "我天下|1|1|50|1|98|0|11|108|3",
        "5",
        "24",
        "100",
        "100",
        "97.12231",
        "97.99867",
        "100",
        "5@3@2@-1@-1|271",
        "1",
        "0|0",
        "0",
        "23.9512",
        "0",
        "1",
        "0@100@null",
        "6",
        "100",
        "1",
        "null",
        "null",
        "-1",
        "1",
        "71",
        "0",
        "null|null|null",
        "6",
        "0|0|0|0.072|0.004|0.001|0.004",
        "1|2|0|0|0|0",
        "0",
        "1",
        "0",
        "18@80@寒门@凌可梦|18@80@寒门@卞舒儿|19@78@童试@秀才|19@92@蜀郡什邡@null|20@89@翊麾校尉(七品)@null|20@80@寒门@钱歆卉|20@92@蜀郡成都@null|21@89@归德司阶(六品)@null|21@80@寒门@田芬宁|21@92@蜀郡绵竹@null|22@89@游骑将军(五品)@null|22@80@寒门@李婧凤|23@92@蜀郡新都@null|24@89@宣威将军(四品)@null|24@80@寒门@纪璟琳",
        "null",
        "null",
        "5",
        "0",
        "2|100000|12",
        "0|0"
    ]

    mod = json.dumps({
        '姓名': Member_now[4].split('|')[0],
        '声誉': Member_now[16],
        '体力': Member_now[21],
        '年龄': Member_now[6],
        '健康': Member_now[18],
        '心情': Member_now[11],
        '魅力': Member_now[20],
        '幸运': Member_now[8],
        '夫妻关系': Member_now[32],
        '#': '#',
        '文才': Member_now[7],
        '武艺': Member_now[8],
        '商才': Member_now[9],
        '艺才': Member_now[10],
        '计谋': Member_now[27],
    }, ensure_ascii=False, indent=2)
    print(mod)

    Member_now[21]=99
    Member_now[20]=99
    Member_now[8]=99

    Member_now[7]=99
    Member_now[8]=98
    Member_now[9]=97
    Member_now[10]=96
    Member_now[27]=95
    print( json.dumps(Member_now, ensure_ascii=False, indent=2))


if __name__ == '__main__':
    jiexi_Member_now()
    # jiexi_Member_qu()
