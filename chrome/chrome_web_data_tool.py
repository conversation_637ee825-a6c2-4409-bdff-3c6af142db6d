import sqlite3
from os import path

fileWebData = path.expandvars(r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default\Web Data')

conn = sqlite3.connect(fileWebData)
cursor = conn.cursor()

execute = cursor.execute("SELECT name, value FROM autofill")
rows = cursor.fetchall()
# 打印结果
for row in rows:
    print(row)

new_list = list(set([row[0] for row in rows]))
for item in new_list:
    print('<input type="search" name="{}"><br>'.format(item))

cursor.close()
conn.close()
